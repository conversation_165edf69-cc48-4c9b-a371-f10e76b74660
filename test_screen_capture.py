"""
Simple test to verify screen capture is working
"""

import cv2
from card_detector import CardDetector

def test_screen_capture():
    """Test basic screen capture functionality"""
    print("Testing screen capture...")
    
    detector = CardDetector()
    
    # Try to capture screen
    image = detector.capture_screen()
    
    if image is None:
        print("❌ Screen capture failed!")
        return False
    
    print(f"✅ Screen capture successful! Image size: {image.shape}")
    
    # Save a test image
    cv2.imwrite('test_capture.png', image)
    print("📸 Test image saved as 'test_capture.png'")
    
    # Show the captured image for 3 seconds
    cv2.imshow('Screen Capture Test', image)
    print("Showing captured screen for 3 seconds...")
    cv2.waitKey(3000)
    cv2.destroyAllWindows()
    
    return True

if __name__ == "__main__":
    print("Screen Capture Test")
    print("=" * 30)
    
    if test_screen_capture():
        print("✅ Screen capture is working correctly!")
    else:
        print("❌ Screen capture test failed!")
