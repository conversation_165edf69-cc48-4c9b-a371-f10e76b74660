"""
Main entry point for the Blackjack Card Counting Bot
"""

import sys
import logging
import argparse
from gui import Card<PERSON>ounterGUI
from config import LOG<PERSON>NG_CONFIG

def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=getattr(logging, LOGGING_CONFIG['level']),
        format=LOGGING_CONFIG['format'],
        handlers=[
            logging.FileHandler(LOGGING_CONFIG['file']),
            logging.StreamHandler(sys.stdout)
        ]
    )

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Blackjack Card Counting Bot')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--no-gui', action='store_true', help='Run without GUI (console mode)')
    
    args = parser.parse_args()
    
    # Setup logging
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("Starting Blackjack Card Counting Bot")
        
        if args.no_gui:
            # Console mode (for testing)
            from card_counter import BlackjackCardCounter
            from card_detector import CardDetector
            
            counter = BlackjackCardCounter()
            detector = CardDetector()
            
            print("Blackjack Card Counter - Console Mode")
            print("Commands: add <card>, reset, stats, bust <hand_value>, quit")

            while True:
                command = input("> ").strip().lower()

                if command == 'quit':
                    break
                elif command == 'reset':
                    counter.reset_count()
                    print("Count reset")
                elif command == 'stats':
                    stats = counter.get_statistics()
                    print(f"Running Count: {stats['running_count']}")
                    print(f"True Count: {stats['true_count']}")
                    print(f"Remaining Decks: {stats['remaining_decks']}")
                    print(f"Cards Dealt: {stats['cards_dealt']}/{stats['total_cards']}")
                    print(f"Advantage: {counter.get_advantage_estimate()}")
                elif command.startswith('bust '):
                    try:
                        hand_value = int(command[5:].strip())
                        probabilities = counter.get_optimal_hand_probabilities(hand_value)
                        print(f"Hand Value: {hand_value}")
                        print(f"Bust Chance: {probabilities['bust']}%")
                        print(f"Safe Hit: {probabilities['safe']}%")
                        print(f"Hit 21: {probabilities['blackjack']}%")
                    except ValueError:
                        print("Invalid hand value. Use: bust <number>")
                elif command.startswith('add '):
                    card = command[4:].strip().upper()
                    if counter.add_card(card):
                        print(f"Added {card}")
                    else:
                        print(f"Invalid card: {card}")
                else:
                    print("Unknown command")
        else:
            # GUI mode
            app = CardCounterGUI()
            app.run()
            
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Application error: {e}", exc_info=True)
        sys.exit(1)
    
    logger.info("Application terminated")

if __name__ == "__main__":
    main()
