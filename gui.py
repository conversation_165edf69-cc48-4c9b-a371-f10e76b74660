"""
GUI for the Blackjack Card Counting Bot
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from typing import Dict
from config import GUI_CONFIG
from card_counter import BlackjackCardCounter
from card_detector import CardDetector

class CardCounterGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        
        # Initialize components
        self.card_counter = BlackjackCardCounter()
        self.card_detector = CardDetector()
        
        # Control variables
        self.is_running = False
        self.detection_thread = None
        self.debug_window = None
        self.show_debug = False
        self.recent_cards = []  # Store recent detected cards
        self.cards_detected_count = 0
        self.detection_start_time = None
        self.activity_dots = 0
        
        # Create GUI elements
        self.create_widgets()
        self.update_display()
        
    def setup_window(self):
        """Configure the main window"""
        self.root.title(GUI_CONFIG['window_title'])
        self.root.geometry(GUI_CONFIG['window_size'])
        self.root.configure(bg=GUI_CONFIG['bg_color'])
        self.root.resizable(True, True)
        
        # Configure style for uniform gray theme
        style = ttk.Style()
        style.theme_use('clam')

        # Configure uniform gray styling without dark borders
        style.configure('Title.TLabel',
                       font=('Arial', 16, 'bold'),
                       background=GUI_CONFIG['bg_color'],
                       foreground=GUI_CONFIG['text_color'])
        style.configure('Stats.TLabel',
                       font=('Arial', GUI_CONFIG['font_size']),
                       background=GUI_CONFIG['bg_color'],
                       foreground=GUI_CONFIG['text_color'])
        style.configure('Value.TLabel',
                       font=('Arial', GUI_CONFIG['font_size'], 'bold'),
                       background=GUI_CONFIG['bg_color'],
                       foreground=GUI_CONFIG['accent_color'])
        style.configure('TLabelframe',
                       background=GUI_CONFIG['bg_color'],
                       borderwidth=0,  # Remove borders
                       relief='flat')
        style.configure('TLabelframe.Label',
                       background=GUI_CONFIG['bg_color'],
                       foreground=GUI_CONFIG['text_color'],
                       font=('Arial', GUI_CONFIG['font_size'], 'bold'))
        style.configure('TFrame',
                       background=GUI_CONFIG['bg_color'],
                       borderwidth=0,
                       relief='flat')
        style.configure('TButton',
                       background=GUI_CONFIG['button_color'],
                       foreground=GUI_CONFIG['text_color'],
                       borderwidth=1,
                       relief='raised')
        style.configure('TEntry',
                       background='white',
                       foreground=GUI_CONFIG['text_color'],
                       borderwidth=1,
                       relief='solid')
        style.configure('TSpinbox',
                       background='white',
                       foreground=GUI_CONFIG['text_color'],
                       borderwidth=1,
                       relief='solid')
        style.configure('TEntry',
                       background='white',
                       foreground=GUI_CONFIG['text_color'])
    
    def create_widgets(self):
        """Create and layout GUI widgets"""
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Title
        title_label = ttk.Label(main_frame, text="Blackjack Card Counter", style='Title.TLabel')
        title_label.pack(pady=(0, 20))

        # Create horizontal layout for main content
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)

        # Left side - original content
        left_frame = ttk.Frame(content_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # Right side - card detection display
        right_frame = ttk.Frame(content_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        
        # Statistics frame (in left frame)
        stats_frame = ttk.LabelFrame(left_frame, text="Statistics", padding=10)
        stats_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Running Count
        self.running_count_var = tk.StringVar(value="0")
        ttk.Label(stats_frame, text="Running Count:", style='Stats.TLabel').grid(row=0, column=0, sticky='w', pady=2)
        ttk.Label(stats_frame, textvariable=self.running_count_var, style='Value.TLabel').grid(row=0, column=1, sticky='e', pady=2)
        
        # True Count
        self.true_count_var = tk.StringVar(value="0.0")
        ttk.Label(stats_frame, text="True Count:", style='Stats.TLabel').grid(row=1, column=0, sticky='w', pady=2)
        ttk.Label(stats_frame, textvariable=self.true_count_var, style='Value.TLabel').grid(row=1, column=1, sticky='e', pady=2)
        
        # Remaining Decks
        self.remaining_decks_var = tk.StringVar(value="8.0")
        ttk.Label(stats_frame, text="Remaining Decks:", style='Stats.TLabel').grid(row=2, column=0, sticky='w', pady=2)
        ttk.Label(stats_frame, textvariable=self.remaining_decks_var, style='Value.TLabel').grid(row=2, column=1, sticky='e', pady=2)
        
        # Cards Dealt
        self.cards_dealt_var = tk.StringVar(value="0 / 416")
        ttk.Label(stats_frame, text="Cards Dealt:", style='Stats.TLabel').grid(row=3, column=0, sticky='w', pady=2)
        ttk.Label(stats_frame, textvariable=self.cards_dealt_var, style='Value.TLabel').grid(row=3, column=1, sticky='e', pady=2)
        
        # Deck Penetration
        self.penetration_var = tk.StringVar(value="0.0%")
        ttk.Label(stats_frame, text="Deck Penetration:", style='Stats.TLabel').grid(row=4, column=0, sticky='w', pady=2)
        ttk.Label(stats_frame, textvariable=self.penetration_var, style='Value.TLabel').grid(row=4, column=1, sticky='e', pady=2)
        
        # Advantage
        self.advantage_var = tk.StringVar(value="NEUTRAL")
        ttk.Label(stats_frame, text="Advantage:", style='Stats.TLabel').grid(row=5, column=0, sticky='w', pady=2)
        self.advantage_label = ttk.Label(stats_frame, textvariable=self.advantage_var, style='Value.TLabel')
        self.advantage_label.grid(row=5, column=1, sticky='e', pady=2)

        # Configure grid weights
        stats_frame.columnconfigure(1, weight=1)

        # Bust Probability frame (in left frame)
        bust_frame = ttk.LabelFrame(left_frame, text="Bust Probability Calculator", padding=10)
        bust_frame.pack(fill=tk.X, pady=(10, 0))

        # Hand value input
        hand_frame = ttk.Frame(bust_frame)
        hand_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(hand_frame, text="Current Hand Value:", style='Stats.TLabel').pack(side=tk.LEFT)
        self.hand_value_var = tk.StringVar(value="12")
        hand_spinbox = tk.Spinbox(hand_frame, from_=4, to=20, textvariable=self.hand_value_var,
                                 width=5, command=self.update_bust_probability)
        hand_spinbox.pack(side=tk.LEFT, padx=(5, 0))

        # Bust probability display - using pack instead of grid to avoid conflicts
        prob_stats_frame = ttk.Frame(bust_frame)
        prob_stats_frame.pack(fill=tk.X)

        # Bust chance
        bust_row = ttk.Frame(prob_stats_frame)
        bust_row.pack(fill=tk.X, pady=2)
        ttk.Label(bust_row, text="Bust Chance on Hit:", style='Stats.TLabel').pack(side=tk.LEFT)
        self.bust_prob_var = tk.StringVar(value="0.0%")
        self.bust_prob_label = ttk.Label(bust_row, textvariable=self.bust_prob_var, style='Value.TLabel')
        self.bust_prob_label.pack(side=tk.RIGHT)

        # Safe hit chance
        safe_row = ttk.Frame(prob_stats_frame)
        safe_row.pack(fill=tk.X, pady=2)
        ttk.Label(safe_row, text="Safe Hit Chance:", style='Stats.TLabel').pack(side=tk.LEFT)
        self.safe_prob_var = tk.StringVar(value="0.0%")
        ttk.Label(safe_row, textvariable=self.safe_prob_var, style='Value.TLabel').pack(side=tk.RIGHT)

        # Hit 21 chance
        twenty_one_row = ttk.Frame(prob_stats_frame)
        twenty_one_row.pack(fill=tk.X, pady=2)
        ttk.Label(twenty_one_row, text="Hit 21 Chance:", style='Stats.TLabel').pack(side=tk.LEFT)
        self.twenty_one_prob_var = tk.StringVar(value="0.0%")
        ttk.Label(twenty_one_row, textvariable=self.twenty_one_prob_var, style='Value.TLabel').pack(side=tk.RIGHT)
        
        # Control buttons frame (in left frame)
        control_frame = ttk.Frame(left_frame)
        control_frame.pack(fill=tk.X, pady=(10, 0))
        
        # Start/Stop button
        self.start_stop_button = ttk.Button(control_frame, text="Start Detection", command=self.toggle_detection)
        self.start_stop_button.pack(side=tk.LEFT, padx=(0, 5))

        # Reset button
        reset_button = ttk.Button(control_frame, text="Reset Count", command=self.reset_count)
        reset_button.pack(side=tk.LEFT, padx=5)

        # Undo button
        undo_button = ttk.Button(control_frame, text="Undo Last", command=self.undo_last_card)
        undo_button.pack(side=tk.LEFT, padx=5)

        # Debug button
        debug_button = ttk.Button(control_frame, text="Show Debug", command=self.toggle_debug)
        debug_button.pack(side=tk.LEFT, padx=5)

        # Region selector button
        region_button = ttk.Button(control_frame, text="Set Region", command=self.open_region_selector)
        region_button.pack(side=tk.LEFT, padx=5)
        
        # Manual add frame (in left frame)
        manual_frame = ttk.LabelFrame(left_frame, text="Manual Card Entry", padding=5)
        manual_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.manual_card_var = tk.StringVar()
        card_entry = ttk.Entry(manual_frame, textvariable=self.manual_card_var, width=10)
        card_entry.pack(side=tk.LEFT, padx=(0, 5))
        
        add_button = ttk.Button(manual_frame, text="Add Card", command=self.add_manual_card)
        add_button.pack(side=tk.LEFT)
        
        # Bind Enter key to add card
        card_entry.bind('<Return>', lambda e: self.add_manual_card())

        # Card Detection Display (right frame)
        detection_frame = ttk.LabelFrame(right_frame, text="Live Card Detection", padding=10)
        detection_frame.pack(fill=tk.BOTH, expand=True)

        # Current card display - larger and more prominent
        current_card_frame = ttk.Frame(detection_frame)
        current_card_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(current_card_frame, text="Current Card:", style='Stats.TLabel').pack(anchor='w')
        self.current_card_var = tk.StringVar(value="None")
        current_card_label = ttk.Label(current_card_frame, textvariable=self.current_card_var,
                                     font=('Arial', 36, 'bold'),
                                     background='white',
                                     foreground='#2d5a2d',
                                     relief='solid',
                                     borderwidth=2,
                                     width=4,
                                     anchor='center')
        current_card_label.pack(pady=(5, 0))

        # Card value indicator
        self.card_value_var = tk.StringVar(value="")
        card_value_label = ttk.Label(current_card_frame, textvariable=self.card_value_var,
                                   font=('Arial', 12, 'bold'),
                                   background=GUI_CONFIG['bg_color'])
        card_value_label.pack(pady=(2, 0))

        # Detection confidence/activity indicator
        activity_frame = ttk.Frame(detection_frame)
        activity_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(activity_frame, text="Detection Activity:", style='Stats.TLabel').pack(anchor='w')
        self.activity_var = tk.StringVar(value="●●●●●")
        activity_label = ttk.Label(activity_frame, textvariable=self.activity_var,
                                 font=('Arial', 14, 'bold'),
                                 background=GUI_CONFIG['bg_color'],
                                 foreground='#666666')
        activity_label.pack(anchor='w', pady=(2, 0))

        # Recent cards history - enhanced
        recent_frame = ttk.Frame(detection_frame)
        recent_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(recent_frame, text="Recent Cards (Last 8):", style='Stats.TLabel').pack(anchor='w')
        self.recent_cards_var = tk.StringVar(value="")
        recent_cards_label = ttk.Label(recent_frame, textvariable=self.recent_cards_var,
                                     font=('Arial', 14, 'bold'),
                                     background=GUI_CONFIG['bg_color'],
                                     foreground=GUI_CONFIG['text_color'],
                                     wraplength=180)
        recent_cards_label.pack(anchor='w', pady=(5, 0))

        # Detection statistics
        stats_detection_frame = ttk.Frame(detection_frame)
        stats_detection_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(stats_detection_frame, text="Detection Stats:", style='Stats.TLabel').pack(anchor='w')

        # Cards detected this session
        self.cards_detected_var = tk.StringVar(value="0")
        cards_detected_frame = ttk.Frame(stats_detection_frame)
        cards_detected_frame.pack(fill=tk.X, pady=(2, 0))
        ttk.Label(cards_detected_frame, text="Cards Detected:", font=('Arial', 9)).pack(side=tk.LEFT)
        ttk.Label(cards_detected_frame, textvariable=self.cards_detected_var,
                font=('Arial', 9, 'bold'), foreground='#2d5a2d').pack(side=tk.RIGHT)

        # Detection rate
        self.detection_rate_var = tk.StringVar(value="0.0/min")
        rate_frame = ttk.Frame(stats_detection_frame)
        rate_frame.pack(fill=tk.X, pady=(2, 0))
        ttk.Label(rate_frame, text="Detection Rate:", font=('Arial', 9)).pack(side=tk.LEFT)
        ttk.Label(rate_frame, textvariable=self.detection_rate_var,
                font=('Arial', 9, 'bold'), foreground='#666666').pack(side=tk.RIGHT)

        # Detection status - enhanced
        status_frame = ttk.Frame(detection_frame)
        status_frame.pack(fill=tk.X)

        ttk.Label(status_frame, text="Status:", style='Stats.TLabel').pack(anchor='w')
        self.detection_status_var = tk.StringVar(value="Stopped")
        detection_status_label = ttk.Label(status_frame, textvariable=self.detection_status_var,
                                         font=('Arial', 12, 'bold'),
                                         background=GUI_CONFIG['bg_color'],
                                         foreground='#8b3a3a')
        detection_status_label.pack(anchor='w', pady=(2, 0))

        # Status bar (main frame)
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(fill=tk.X, pady=(10, 0))
    
    def toggle_detection(self):
        """Start or stop card detection"""
        if not self.is_running:
            self.start_detection()
        else:
            self.stop_detection()
    
    def start_detection(self):
        """Start the card detection thread"""
        self.is_running = True
        self.start_stop_button.config(text="Stop Detection")
        self.status_var.set("Detection running...")
        self.detection_status_var.set("🟢 ACTIVE")
        self.detection_start_time = time.time()
        self.cards_detected_count = 0

        self.detection_thread = threading.Thread(target=self.detection_loop, daemon=True)
        self.detection_thread.start()
    
    def stop_detection(self):
        """Stop card detection"""
        self.is_running = False
        self.start_stop_button.config(text="Start Detection")
        self.status_var.set("Detection stopped")
        self.detection_status_var.set("🔴 STOPPED")
        self.current_card_var.set("None")
        self.activity_var.set("●●●●●")
        self.card_value_var.set("")
    
    def detection_loop(self):
        """Main detection loop running in separate thread"""
        import cv2

        while self.is_running:
            try:
                # Capture screen and detect cards
                image = self.card_detector.capture_screen()
                if image is not None:
                    new_cards = self.card_detector.detect_new_cards(image)

                    # Add detected cards to counter and update display
                    for card in new_cards:
                        if self.card_counter.add_card(card):
                            self.cards_detected_count += 1

                            # Update current card display
                            self.root.after(0, lambda c=card: self.current_card_var.set(c))
                            self.root.after(0, lambda c=card: self.status_var.set(f"Detected: {c}"))

                            # Update card value indicator
                            card_value = self.card_counter.card_values.get(card, 0)
                            if card_value > 0:
                                value_text = f"(+{card_value})"
                                color = '#2d5a2d'  # Green for positive
                            elif card_value < 0:
                                value_text = f"({card_value})"
                                color = '#8b3a3a'  # Red for negative
                            else:
                                value_text = "(0)"
                                color = '#666666'  # Gray for neutral

                            self.root.after(0, lambda t=value_text: self.card_value_var.set(t))

                            # Add to recent cards list
                            self.recent_cards.append(card)
                            if len(self.recent_cards) > 8:  # Keep only last 8 cards
                                self.recent_cards = self.recent_cards[-8:]

                            # Update recent cards display with spacing
                            recent_text = "  ".join(self.recent_cards)
                            self.root.after(0, lambda t=recent_text: self.recent_cards_var.set(t))

                            # Update detection stats
                            self.root.after(0, lambda: self.cards_detected_var.set(str(self.cards_detected_count)))

                            # Calculate detection rate
                            if self.detection_start_time:
                                elapsed_minutes = (time.time() - self.detection_start_time) / 60
                                if elapsed_minutes > 0:
                                    rate = self.cards_detected_count / elapsed_minutes
                                    rate_text = f"{rate:.1f}/min"
                                    self.root.after(0, lambda t=rate_text: self.detection_rate_var.set(t))

                    # Update activity indicator
                    self.activity_dots = (self.activity_dots + 1) % 6
                    activity_pattern = "●" * self.activity_dots + "○" * (5 - self.activity_dots)
                    self.root.after(0, lambda p=activity_pattern: self.activity_var.set(p))

                    # If no new cards detected, show scanning status
                    if not new_cards:
                        self.root.after(0, lambda: self.current_card_var.set("Scanning..."))
                        self.root.after(0, lambda: self.card_value_var.set(""))

                    # Show debug window if enabled
                    if self.show_debug:
                        debug_img = self.card_detector.get_debug_image(image)
                        if debug_img is not None:
                            cv2.imshow('Card Detection Debug', debug_img)
                            cv2.waitKey(1)

                # Update display
                self.root.after(0, self.update_display)

                # Sleep to prevent excessive CPU usage
                time.sleep(0.2)

            except Exception as e:
                self.root.after(0, lambda: self.status_var.set(f"Error: {str(e)}"))
                time.sleep(1)
    
    def update_display(self):
        """Update the statistics display"""
        stats = self.card_counter.get_statistics()

        self.running_count_var.set(str(stats['running_count']))
        self.true_count_var.set(str(stats['true_count']))
        self.remaining_decks_var.set(str(stats['remaining_decks']))
        self.cards_dealt_var.set(f"{stats['cards_dealt']} / {stats['total_cards']}")
        self.penetration_var.set(f"{stats['deck_penetration']}%")

        advantage = self.card_counter.get_advantage_estimate()
        self.advantage_var.set(advantage)

        # Color code the advantage
        if advantage == "FAVORABLE":
            self.advantage_label.configure(foreground='#2d5a2d')  # Dark Green
        elif advantage == "UNFAVORABLE":
            self.advantage_label.configure(foreground='#8b3a3a')  # Dark Red
        elif "SLIGHTLY" in advantage:
            self.advantage_label.configure(foreground='#8b6914')  # Dark Orange
        else:
            self.advantage_label.configure(foreground=GUI_CONFIG['accent_color'])  # Gray

        # Update bust probability
        self.update_bust_probability()

    def update_bust_probability(self):
        """Update bust probability based on current hand value"""
        try:
            hand_value = int(self.hand_value_var.get())
            probabilities = self.card_counter.get_optimal_hand_probabilities(hand_value)

            self.bust_prob_var.set(f"{probabilities['bust']}%")
            self.safe_prob_var.set(f"{probabilities['safe']}%")
            self.twenty_one_prob_var.set(f"{probabilities['blackjack']}%")

            # Color code bust probability
            bust_prob = probabilities['bust']
            if bust_prob >= 70:
                self.bust_prob_label.configure(foreground='#8b3a3a')  # Dark Red - High risk
            elif bust_prob >= 40:
                self.bust_prob_label.configure(foreground='#8b6914')  # Dark Orange - Medium risk
            else:
                self.bust_prob_label.configure(foreground='#2d5a2d')  # Dark Green - Low risk

        except ValueError:
            # Invalid hand value
            self.bust_prob_var.set("N/A")
            self.safe_prob_var.set("N/A")
            self.twenty_one_prob_var.set("N/A")
    
    def reset_count(self):
        """Reset the card counter"""
        if messagebox.askyesno("Reset Count", "Are you sure you want to reset the count?"):
            self.card_counter.reset_count()
            self.update_display()
            self.status_var.set("Count reset")
    
    def undo_last_card(self):
        """Undo the last added card"""
        if self.card_counter.remove_last_card():
            self.update_display()
            self.status_var.set("Last card removed")
        else:
            self.status_var.set("No cards to remove")
    
    def add_manual_card(self):
        """Add a card manually"""
        card = self.manual_card_var.get().strip().upper()
        if card:
            if self.card_counter.add_card(card):
                self.update_display()
                self.status_var.set(f"Added: {card}")
                self.manual_card_var.set("")
            else:
                self.status_var.set(f"Invalid card: {card}")

    def toggle_debug(self):
        """Toggle debug window display"""
        import cv2

        self.show_debug = not self.show_debug
        if not self.show_debug:
            cv2.destroyAllWindows()
            self.status_var.set("Debug window closed")
        else:
            self.status_var.set("Debug window enabled - showing card detection")

    def open_region_selector(self):
        """Open the region selector window"""
        import subprocess
        import sys

        try:
            # Launch region selector as separate process
            subprocess.Popen([sys.executable, "region_selector.py"])
            self.status_var.set("Region selector opened - configure detection area")
        except Exception as e:
            self.status_var.set(f"Failed to open region selector: {e}")

    def run(self):
        """Start the GUI main loop"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

    def on_closing(self):
        """Handle window closing"""
        import cv2
        self.stop_detection()
        cv2.destroyAllWindows()
        self.root.destroy()

if __name__ == "__main__":
    app = CardCounterGUI()
    app.run()
