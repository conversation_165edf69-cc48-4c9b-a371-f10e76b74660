"""
Setup script for Blackjack Card Counting Bot
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install requirements: {e}")
        return False

def check_tesseract():
    """Check if Tesseract is installed"""
    try:
        subprocess.run(["tesseract", "--version"], capture_output=True, check=True)
        print("✓ Tesseract OCR found")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠ Tesseract OCR not found. EasyOCR will be used instead.")
        print("  To install Tesseract: https://github.com/UB-Mannheim/tesseract/wiki")
        return False

def create_config_backup():
    """Create a backup of the default config"""
    if os.path.exists("config.py"):
        try:
            with open("config.py", "r") as f:
                content = f.read()
            with open("config_default.py", "w") as f:
                f.write(content)
            print("✓ Config backup created")
        except Exception as e:
            print(f"⚠ Could not create config backup: {e}")

def main():
    """Main setup function"""
    print("Blackjack Card Counting Bot - Setup")
    print("=" * 40)
    
    # Install requirements
    if not install_requirements():
        print("Setup failed. Please install requirements manually.")
        return False
    
    # Check Tesseract
    check_tesseract()
    
    # Create config backup
    create_config_backup()
    
    print("\n" + "=" * 40)
    print("Setup completed!")
    print("\nNext steps:")
    print("1. Edit config.py to set your screen region")
    print("2. Run: python main.py")
    print("3. Click 'Start Detection' to begin counting")
    
    return True

if __name__ == "__main__":
    main()
