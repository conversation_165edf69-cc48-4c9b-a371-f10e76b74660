import tkinter as tk
from tkinter import messagebox, ttk
import pyautogui
from PIL import Image, ImageTk
import json
import re
import threading
import time

class RegionSelector:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Card Detection Region Selector")
        self.root.attributes('-topmost', True)
        self.root.geometry("800x700")

        # Take initial screenshot
        self.screenshot = pyautogui.screenshot()
        self.scale_factor = 0.4  # Smaller scale for better fit

        # Selection variables
        self.start_x = None
        self.start_y = None
        self.rect_id = None
        self.selected_region = None
        self.live_preview = False

        self.create_widgets()
        self.update_screenshot()

    def create_widgets(self):
        # Main frame
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Control panel
        control_frame = tk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))

        # Manual input frame
        manual_frame = tk.LabelFrame(control_frame, text="Manual Region Input", padx=5, pady=5)
        manual_frame.pack(fill=tk.X, pady=(0, 10))

        # Input fields
        input_grid = tk.Frame(manual_frame)
        input_grid.pack(fill=tk.X)

        tk.Label(input_grid, text="Left:").grid(row=0, column=0, sticky='w', padx=(0, 5))
        self.left_var = tk.StringVar(value="0")
        tk.Entry(input_grid, textvariable=self.left_var, width=8).grid(row=0, column=1, padx=(0, 10))

        tk.Label(input_grid, text="Top:").grid(row=0, column=2, sticky='w', padx=(0, 5))
        self.top_var = tk.StringVar(value="0")
        tk.Entry(input_grid, textvariable=self.top_var, width=8).grid(row=0, column=3, padx=(0, 10))

        tk.Label(input_grid, text="Width:").grid(row=1, column=0, sticky='w', padx=(0, 5))
        self.width_var = tk.StringVar(value="800")
        tk.Entry(input_grid, textvariable=self.width_var, width=8).grid(row=1, column=1, padx=(0, 10))

        tk.Label(input_grid, text="Height:").grid(row=1, column=2, sticky='w', padx=(0, 5))
        self.height_var = tk.StringVar(value="600")
        tk.Entry(input_grid, textvariable=self.height_var, width=8).grid(row=1, column=3, padx=(0, 10))

        # Preset buttons
        preset_frame = tk.Frame(manual_frame)
        preset_frame.pack(fill=tk.X, pady=(10, 0))

        tk.Button(preset_frame, text="Full Screen", command=self.set_full_screen).pack(side=tk.LEFT, padx=(0, 5))
        tk.Button(preset_frame, text="Center Half", command=self.set_center_half).pack(side=tk.LEFT, padx=5)
        tk.Button(preset_frame, text="Update Preview", command=self.update_manual_selection).pack(side=tk.LEFT, padx=5)

        # Buttons frame
        button_frame = tk.Frame(control_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        tk.Button(button_frame, text="Save Region", command=self.save_region, bg='#4CAF50', fg='white').pack(side=tk.LEFT, padx=(0, 5))
        tk.Button(button_frame, text="Refresh Screenshot", command=self.update_screenshot).pack(side=tk.LEFT, padx=5)

        # Live preview toggle
        self.live_var = tk.BooleanVar()
        tk.Checkbutton(button_frame, text="Live Preview", variable=self.live_var,
                      command=self.toggle_live_preview).pack(side=tk.LEFT, padx=5)

        tk.Button(button_frame, text="Cancel", command=self.root.quit).pack(side=tk.RIGHT)

        # Canvas frame
        canvas_frame = tk.Frame(main_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True)

        # Create canvas with scrollbars
        self.canvas = tk.Canvas(canvas_frame, bg='white')
        v_scrollbar = tk.Scrollbar(canvas_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        h_scrollbar = tk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL, command=self.canvas.xview)
        self.canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Bind events
        self.canvas.bind("<Button-1>", self.start_selection)
        self.canvas.bind("<B1-Motion>", self.update_selection)
        self.canvas.bind("<ButtonRelease-1>", self.end_selection)

        # Instructions
        instructions = tk.Label(
            main_frame,
            text="Click and drag on the image to select region, or use manual input above.\nFor live blackjack, select the area where cards appear.",
            font=("Arial", 10),
            fg='blue'
        )
        instructions.pack(pady=(10, 0))

    def update_screenshot(self):
        """Take a new screenshot and update the display"""
        self.screenshot = pyautogui.screenshot()
        display_size = (
            int(self.screenshot.width * self.scale_factor),
            int(self.screenshot.height * self.scale_factor)
        )
        self.display_image = self.screenshot.resize(display_size, Image.Resampling.LANCZOS)
        self.photo = ImageTk.PhotoImage(self.display_image)

        # Update canvas
        self.canvas.delete("all")
        self.canvas.create_image(0, 0, anchor=tk.NW, image=self.photo)
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

        # Redraw selection if exists
        if self.selected_region:
            self.draw_region_on_canvas()

    def set_full_screen(self):
        """Set region to full screen"""
        screen_width, screen_height = pyautogui.size()
        self.left_var.set("0")
        self.top_var.set("0")
        self.width_var.set(str(screen_width))
        self.height_var.set(str(screen_height))
        self.update_manual_selection()

    def set_center_half(self):
        """Set region to center half of screen"""
        screen_width, screen_height = pyautogui.size()
        width = screen_width // 2
        height = screen_height // 2
        left = screen_width // 4
        top = screen_height // 4

        self.left_var.set(str(left))
        self.top_var.set(str(top))
        self.width_var.set(str(width))
        self.height_var.set(str(height))
        self.update_manual_selection()

    def update_manual_selection(self):
        """Update selection based on manual input"""
        try:
            left = int(self.left_var.get())
            top = int(self.top_var.get())
            width = int(self.width_var.get())
            height = int(self.height_var.get())

            self.selected_region = {
                'left': left,
                'top': top,
                'width': width,
                'height': height
            }

            self.draw_region_on_canvas()

        except ValueError:
            messagebox.showerror("Invalid Input", "Please enter valid numbers for all fields.")

    def draw_region_on_canvas(self):
        """Draw the selected region on the canvas"""
        if not self.selected_region:
            return

        # Convert to canvas coordinates
        canvas_left = int(self.selected_region['left'] * self.scale_factor)
        canvas_top = int(self.selected_region['top'] * self.scale_factor)
        canvas_width = int(self.selected_region['width'] * self.scale_factor)
        canvas_height = int(self.selected_region['height'] * self.scale_factor)

        # Remove old rectangle
        if self.rect_id:
            self.canvas.delete(self.rect_id)

        # Draw new rectangle
        self.rect_id = self.canvas.create_rectangle(
            canvas_left, canvas_top,
            canvas_left + canvas_width, canvas_top + canvas_height,
            outline='red', width=3, fill='', stipple='gray50'
        )

        # Add label
        self.canvas.create_text(
            canvas_left + 5, canvas_top + 5,
            text=f"{self.selected_region['width']}x{self.selected_region['height']}",
            anchor='nw', fill='red', font=('Arial', 10, 'bold')
        )

    def toggle_live_preview(self):
        """Toggle live preview mode"""
        self.live_preview = self.live_var.get()
        if self.live_preview:
            self.live_update()

    def live_update(self):
        """Continuously update screenshot in live mode"""
        if self.live_preview:
            self.update_screenshot()
            self.root.after(1000, self.live_update)  # Update every second

    def start_selection(self, event):
        self.start_x = event.x
        self.start_y = event.y

    def update_selection(self, event):
        if self.rect_id:
            self.canvas.delete(self.rect_id)

        self.rect_id = self.canvas.create_rectangle(
            self.start_x, self.start_y, event.x, event.y,
            outline='red', width=2
        )

    def end_selection(self, event):
        if self.start_x and self.start_y:
            # Convert to actual screen coordinates
            actual_left = int(min(self.start_x, event.x) / self.scale_factor)
            actual_top = int(min(self.start_y, event.y) / self.scale_factor)
            actual_width = int(abs(event.x - self.start_x) / self.scale_factor)
            actual_height = int(abs(event.y - self.start_y) / self.scale_factor)

            self.selected_region = {
                'left': actual_left,
                'top': actual_top,
                'width': actual_width,
                'height': actual_height
            }

            # Update manual input fields
            self.left_var.set(str(actual_left))
            self.top_var.set(str(actual_top))
            self.width_var.set(str(actual_width))
            self.height_var.set(str(actual_height))

    def save_region(self):
        # Get current values from manual input
        try:
            self.selected_region = {
                'left': int(self.left_var.get()),
                'top': int(self.top_var.get()),
                'width': int(self.width_var.get()),
                'height': int(self.height_var.get())
            }
        except ValueError:
            messagebox.showerror("Invalid Input", "Please enter valid numbers for all fields.")
            return

        # Update config.py
        try:
            with open('config.py', 'r') as f:
                content = f.read()

            # Replace the SCREEN_REGION section
            pattern = r"SCREEN_REGION = \{[^}]+\}"
            new_region = f"""SCREEN_REGION = {{
    'top': {self.selected_region['top']},
    'left': {self.selected_region['left']},
    'width': {self.selected_region['width']},
    'height': {self.selected_region['height']}
}}"""

            updated_content = re.sub(pattern, new_region, content, flags=re.DOTALL)

            with open('config.py', 'w') as f:
                f.write(updated_content)

            messagebox.showinfo("Success", f"Region saved to config.py!\n\nRegion: {self.selected_region}")
            self.root.quit()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save region: {e}")

    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    selector = RegionSelector()
    selector.run()
