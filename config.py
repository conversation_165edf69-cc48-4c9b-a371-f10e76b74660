"""
Configuration settings for the Blackjack Card Counting Bot
"""

# Deck Configuration
TOTAL_DECKS = 8
CARDS_PER_DECK = 52
TOTAL_CARDS = TOTAL_DECKS * CARDS_PER_DECK

# Card Values for Hi-Lo Counting System
CARD_VALUES = {
    '2': 1, '3': 1, '4': 1, '5': 1, '6': 1,  # Low cards: +1
    '7': 0, '8': 0, '9': 0,                   # Neutral cards: 0
    '10': -1, 'J': -1, 'Q': -1, 'K': -1, 'A': -1  # High cards: -1
}

# Screen Capture Settings
SCREEN_REGION = {
    'top': 170,
    'left': 320,
    'width': 1335,
    'height': 875
}

# Card Detection Settings
CARD_DETECTION = {
    'min_card_area': 1000,  # Increased for better card detection
    'max_card_area': 12000,  # Increased for larger cards
    'card_aspect_ratio_min': 0.5,  # More lenient for card shapes
    'card_aspect_ratio_max': 1.0,  # Standard playing card ratio
    'confidence_threshold': 0.3  # Lower threshold for better detection
}

# GUI Settings
GUI_CONFIG = {
    'window_title': 'Blackjack Card Counter',
    'window_size': '800x600',
    'update_interval': 100,  # milliseconds
    'font_size': 11,
    'bg_color': '#e8e8e8',  # Uniform light gray
    'text_color': '#2c2c2c',  # Dark gray text
    'accent_color': '#5a5a5a',  # Medium gray for values
    'frame_color': '#d0d0d0',  # Slightly darker gray for frames
    'button_color': '#c0c0c0'  # Button gray
}

# OCR Settings
OCR_CONFIG = {
    'use_easyocr': True,  # Set to False to use Tesseract
    'languages': ['en'],
    'confidence_threshold': 0.5
}

# Card Template Paths (for template matching)
CARD_TEMPLATES = {
    'suits': ['hearts', 'diamonds', 'clubs', 'spades'],
    'ranks': ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K']
}

# Logging
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'file': 'card_counter.log'
}
