"""
Test script for card detection functionality
"""

import cv2
import numpy as np
from card_detector import CardDetector
from card_counter import Blackjack<PERSON>ard<PERSON>ounter

def test_card_detection():
    """Test the card detection with screen capture"""
    print("Testing Card Detection...")
    
    detector = CardDetector()
    counter = BlackjackCardCounter()
    
    print("Capturing screen...")
    image = detector.capture_screen()
    
    if image is None:
        print("Failed to capture screen")
        return
    
    print(f"Screen captured: {image.shape}")
    
    # Detect card regions
    regions = detector.detect_card_regions(image)
    print(f"Found {len(regions)} potential card regions")
    
    # Try to extract ranks
    detected_cards = []
    for i, region in enumerate(regions):
        rank = detector.extract_card_rank(image, region)
        if rank:
            detected_cards.append(rank)
            print(f"Region {i}: Detected card rank '{rank}'")
        else:
            print(f"Region {i}: No rank detected")
    
    print(f"Total cards detected: {detected_cards}")
    
    # Show debug image
    debug_img = detector.get_debug_image(image)
    if debug_img is not None:
        cv2.imshow('Card Detection Test', debug_img)
        print("Press any key to close debug window...")
        cv2.waitKey(0)
        cv2.destroyAllWindows()
    
    # Test adding cards to counter
    print("\nTesting card counter...")
    for card in detected_cards:
        if counter.add_card(card):
            print(f"Added {card} to counter")
    
    stats = counter.get_statistics()
    print(f"Running Count: {stats['running_count']}")
    print(f"True Count: {stats['true_count']}")
    print(f"Remaining Decks: {stats['remaining_decks']}")
    
    # Test bust probability
    print("\nTesting bust probability calculation...")
    for hand_value in [12, 16, 19]:
        probs = counter.get_optimal_hand_probabilities(hand_value)
        print(f"Hand {hand_value}: Bust={probs['bust']}%, Safe={probs['safe']}%, 21={probs['blackjack']}%")

def test_manual_cards():
    """Test with manual card input"""
    print("\nTesting manual card input...")
    
    counter = BlackjackCardCounter()
    
    # Simulate some cards being dealt
    test_cards = ['K', 'Q', '5', '6', 'A', '10', '2', '3']
    
    for card in test_cards:
        if counter.add_card(card):
            print(f"Added {card}")
        
        stats = counter.get_statistics()
        print(f"  Running Count: {stats['running_count']}, True Count: {stats['true_count']}")
    
    print(f"\nFinal advantage: {counter.get_advantage_estimate()}")

if __name__ == "__main__":
    print("Blackjack Card Counter - Detection Test")
    print("=" * 50)
    
    try:
        test_card_detection()
        test_manual_cards()
        
        print("\nTest completed successfully!")
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
