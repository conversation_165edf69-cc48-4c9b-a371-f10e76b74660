"""
Card counting engine for blackjack using Hi-Lo system
"""

import logging
from typing import Dict, List, Tuple
from config import CARD_VALUES, TOTAL_DECKS, CARDS_PER_DECK

class BlackjackCardCounter:
    def __init__(self, total_decks: int = TOTAL_DECKS):
        self.total_decks = total_decks
        self.cards_per_deck = CARDS_PER_DECK
        self.total_cards = total_decks * self.cards_per_deck
        
        # Initialize deck tracking
        self.remaining_cards = self._initialize_deck()
        self.dealt_cards = []
        
        # Counting variables
        self.running_count = 0
        self.cards_dealt = 0
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    def _initialize_deck(self) -> Dict[str, int]:
        """Initialize the deck with all cards"""
        deck = {}
        ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K']
        for rank in ranks:
            deck[rank] = self.total_decks * 4  # 4 suits per deck
        return deck
    
    def add_card(self, card_rank: str) -> bool:
        """
        Add a detected card to the count
        Returns True if card was successfully added, False otherwise
        """
        if not self._is_valid_card(card_rank):
            self.logger.warning(f"Invalid card rank: {card_rank}")
            return False
            
        if self.remaining_cards.get(card_rank, 0) <= 0:
            self.logger.warning(f"No more {card_rank} cards remaining in deck")
            return False
        
        # Update deck tracking
        self.remaining_cards[card_rank] -= 1
        self.dealt_cards.append(card_rank)
        self.cards_dealt += 1
        
        # Update running count
        card_value = CARD_VALUES.get(card_rank, 0)
        self.running_count += card_value
        
        self.logger.info(f"Card added: {card_rank}, Running count: {self.running_count}")
        return True
    
    def _is_valid_card(self, card_rank: str) -> bool:
        """Check if the card rank is valid"""
        return card_rank in CARD_VALUES
    
    def get_true_count(self) -> float:
        """Calculate the true count (running count / remaining decks)"""
        remaining_decks = self.get_remaining_decks()
        if remaining_decks <= 0:
            return 0.0
        return round(self.running_count / remaining_decks, 2)
    
    def get_remaining_decks(self) -> float:
        """Calculate remaining decks based on dealt cards"""
        remaining_cards = self.total_cards - self.cards_dealt
        return round(remaining_cards / self.cards_per_deck, 2)
    
    def get_deck_penetration(self) -> float:
        """Calculate deck penetration percentage"""
        return round((self.cards_dealt / self.total_cards) * 100, 2)
    
    def get_statistics(self) -> Dict:
        """Get comprehensive counting statistics"""
        return {
            'running_count': self.running_count,
            'true_count': self.get_true_count(),
            'cards_dealt': self.cards_dealt,
            'total_cards': self.total_cards,
            'remaining_decks': self.get_remaining_decks(),
            'total_decks': self.total_decks,
            'deck_penetration': self.get_deck_penetration(),
            'remaining_cards_by_rank': self.remaining_cards.copy()
        }
    
    def reset_count(self):
        """Reset the counter for a new shoe"""
        self.remaining_cards = self._initialize_deck()
        self.dealt_cards = []
        self.running_count = 0
        self.cards_dealt = 0
        self.logger.info("Card counter reset")
    
    def remove_last_card(self) -> bool:
        """Remove the last added card (undo functionality)"""
        if not self.dealt_cards:
            return False
            
        last_card = self.dealt_cards.pop()
        self.remaining_cards[last_card] += 1
        self.cards_dealt -= 1
        
        # Reverse the count
        card_value = CARD_VALUES.get(last_card, 0)
        self.running_count -= card_value
        
        self.logger.info(f"Removed last card: {last_card}, Running count: {self.running_count}")
        return True
    
    def get_advantage_estimate(self) -> str:
        """Get a simple advantage estimate based on true count"""
        true_count = self.get_true_count()

        if true_count >= 2:
            return "FAVORABLE"
        elif true_count >= 1:
            return "SLIGHTLY FAVORABLE"
        elif true_count <= -2:
            return "UNFAVORABLE"
        elif true_count <= -1:
            return "SLIGHTLY UNFAVORABLE"
        else:
            return "NEUTRAL"

    def calculate_bust_probability(self, current_hand_value: int) -> float:
        """
        Calculate the probability of busting when hitting with current hand value
        Takes into account the remaining cards in the deck
        """
        if current_hand_value >= 21:
            return 0.0 if current_hand_value == 21 else 100.0

        if current_hand_value < 12:
            return 0.0  # Cannot bust with hand value less than 12

        # Calculate remaining cards that would cause a bust
        bust_threshold = 22 - current_hand_value
        bust_cards = 0
        total_remaining = 0

        for rank, count in self.remaining_cards.items():
            total_remaining += count

            # Determine card value for bust calculation
            if rank == 'A':
                card_value = 1  # Ace as 1 for bust calculation
            elif rank in ['J', 'Q', 'K']:
                card_value = 10
            else:
                card_value = int(rank)

            # Count cards that would cause bust
            if card_value >= bust_threshold:
                bust_cards += count

        if total_remaining == 0:
            return 0.0

        bust_probability = (bust_cards / total_remaining) * 100
        return round(bust_probability, 1)

    def get_optimal_hand_probabilities(self, current_hand_value: int) -> Dict[str, float]:
        """
        Get probabilities for different outcomes when hitting
        """
        if current_hand_value >= 21:
            return {
                'bust': 100.0 if current_hand_value > 21 else 0.0,
                'safe': 0.0 if current_hand_value > 21 else 100.0,
                'blackjack': 100.0 if current_hand_value == 21 else 0.0
            }

        safe_cards = 0
        bust_cards = 0
        twenty_one_cards = 0
        total_remaining = 0

        target_for_21 = 21 - current_hand_value

        for rank, count in self.remaining_cards.items():
            total_remaining += count

            # Determine card value
            if rank == 'A':
                card_value = 1  # Conservative estimate
            elif rank in ['J', 'Q', 'K']:
                card_value = 10
            else:
                card_value = int(rank)

            if card_value == target_for_21:
                twenty_one_cards += count
            elif current_hand_value + card_value < 21:
                safe_cards += count
            elif current_hand_value + card_value > 21:
                bust_cards += count

        if total_remaining == 0:
            return {'bust': 0.0, 'safe': 0.0, 'blackjack': 0.0}

        return {
            'bust': round((bust_cards / total_remaining) * 100, 1),
            'safe': round((safe_cards / total_remaining) * 100, 1),
            'blackjack': round((twenty_one_cards / total_remaining) * 100, 1)
        }
