"""
Test script to verify screen capture and card detection fixes
"""

import cv2
import numpy as np
from card_detector import CardDetector
from config import SCREEN_REGION
import time

def test_screen_capture():
    """Test if screen capture is working without threading issues"""
    print("Testing screen capture...")
    
    detector = CardDetector()
    
    # Test multiple captures
    for i in range(5):
        print(f"Capture {i+1}/5...")
        image = detector.capture_screen()
        
        if image is not None:
            print(f"✓ Capture {i+1} successful - Image shape: {image.shape}")
            
            # Save a test image
            if i == 0:
                cv2.imwrite('test_capture_fixed.png', image)
                print("✓ Test image saved as 'test_capture_fixed.png'")
                
        else:
            print(f"✗ Capture {i+1} failed")
            
        time.sleep(0.5)
    
    print("\nTesting card detection...")
    
    # Test card detection on the last captured image
    if image is not None:
        regions = detector.detect_card_regions(image)
        print(f"Found {len(regions)} potential card regions")
        
        if len(regions) > 0:
            print("Card regions found:")
            for i, (x, y, w, h) in enumerate(regions[:5]):  # Show first 5
                print(f"  Region {i+1}: x={x}, y={y}, w={w}, h={h}, area={w*h}")
                
        # Test full detection pipeline
        new_cards = detector.detect_new_cards(image)
        if new_cards:
            print(f"Detected cards: {new_cards}")
        else:
            print("No cards detected in current frame")
            
    print(f"\nScreen region being captured: {SCREEN_REGION}")
    print("Test completed!")

if __name__ == "__main__":
    test_screen_capture()
