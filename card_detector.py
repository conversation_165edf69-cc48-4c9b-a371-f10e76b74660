"""
Screen capture and card detection module
"""

import cv2
import numpy as np
import easyocr
import pytesseract
import logging
from typing import List, Tu<PERSON>, Optional
from config import SCREEN_REGION, CARD_DETECTION, OCR_CONFIG

class CardDetector:
    def __init__(self):
        self.screen_capture = None  # Initialize later to avoid threading issues
        self.setup_ocr()
        self.logger = logging.getLogger(__name__)

        # Card detection parameters
        self.min_area = CARD_DETECTION['min_card_area']
        self.max_area = CARD_DETECTION['max_card_area']
        self.min_aspect = CARD_DETECTION['card_aspect_ratio_min']
        self.max_aspect = CARD_DETECTION['card_aspect_ratio_max']

        # Store previous frame for comparison
        self.previous_frame = None
        self.detected_cards_history = []
        
    def setup_ocr(self):
        """Initialize OCR engine"""
        if OCR_CONFIG['use_easyocr']:
            self.ocr_reader = easyocr.Reader(OCR_CONFIG['languages'])
        else:
            # Configure Tesseract
            pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
    
    def capture_screen(self) -> np.ndarray:
        """Capture the specified screen region"""
        try:
            # Use PIL ImageGrab as primary method to avoid threading issues
            from PIL import ImageGrab
            bbox = (SCREEN_REGION['left'], SCREEN_REGION['top'],
                   SCREEN_REGION['left'] + SCREEN_REGION['width'],
                   SCREEN_REGION['top'] + SCREEN_REGION['height'])
            screenshot = ImageGrab.grab(bbox)
            img = np.array(screenshot)
            return cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
        except Exception as e:
            self.logger.error(f"Screen capture failed: {e}")
            # Fallback to mss with fresh instance
            try:
                import mss
                with mss.mss() as screen_capture:
                    screenshot = screen_capture.grab(SCREEN_REGION)
                    img = np.array(screenshot)
                    return cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
            except Exception as e2:
                self.logger.error(f"Alternative screen capture also failed: {e2}")
                return None
    
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """Preprocess image for better card detection in live blackjack"""
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Enhance contrast using CLAHE optimized for cards
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)

        # Apply bilateral filter to reduce noise while preserving edges
        filtered = cv2.bilateralFilter(enhanced, 9, 75, 75)

        # Use multiple threshold methods and combine them
        # Method 1: Adaptive threshold
        thresh1 = cv2.adaptiveThreshold(
            filtered, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
            cv2.THRESH_BINARY, 11, 2
        )

        # Method 2: Otsu's threshold
        _, thresh2 = cv2.threshold(filtered, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # Combine thresholds
        thresh = cv2.bitwise_and(thresh1, thresh2)

        # Apply morphological operations optimized for card shapes
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        thresh = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        thresh = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel)

        return thresh
    
    def detect_card_regions(self, image: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """Detect potential card regions in the image"""
        processed = self.preprocess_image(image)

        # Find contours
        contours, _ = cv2.findContours(processed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        card_regions = []

        for contour in contours:
            # Calculate area and aspect ratio
            area = cv2.contourArea(contour)
            if area < self.min_area or area > self.max_area:
                continue

            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = w / h if h > 0 else 0

            # Check if it looks like a card (more lenient aspect ratio)
            if self.min_aspect <= aspect_ratio <= self.max_aspect:
                # Additional checks for card-like properties
                perimeter = cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, 0.02 * perimeter, True)

                # Cards should have roughly rectangular shape (4 corners)
                if len(approx) >= 4:
                    card_regions.append((x, y, w, h))

        # Sort by area (largest first) and return top candidates
        card_regions.sort(key=lambda r: r[2] * r[3], reverse=True)
        return card_regions[:10]  # Limit to top 10 candidates
    
    def extract_card_rank(self, image: np.ndarray, region: Tuple[int, int, int, int]) -> Optional[str]:
        """Extract card rank from a detected card region"""
        x, y, w, h = region
        
        # Extract the top-left corner where rank is typically located
        rank_region_height = int(h * 0.3)  # Top 30% of card
        rank_region_width = int(w * 0.3)   # Left 30% of card
        
        card_roi = image[y:y+rank_region_height, x:x+rank_region_width]
        
        if card_roi.size == 0:
            return None
        
        try:
            if OCR_CONFIG['use_easyocr']:
                results = self.ocr_reader.readtext(card_roi)
                if results:
                    text = results[0][1].strip().upper()
                    return self._normalize_card_rank(text)
            else:
                # Use Tesseract
                text = pytesseract.image_to_string(
                    card_roi, 
                    config='--psm 8 -c tessedit_char_whitelist=A23456789JQK10'
                ).strip().upper()
                return self._normalize_card_rank(text)
                
        except Exception as e:
            self.logger.debug(f"OCR failed for region: {e}")
            
        return None
    
    def _normalize_card_rank(self, text: str) -> Optional[str]:
        """Normalize detected text to valid card ranks"""
        text = text.replace(' ', '').replace('.', '').replace(',', '')
        
        # Common OCR corrections
        corrections = {
            '0': '10', 'O': '10', 'IO': '10', '1O': '10',
            'I': '1', 'l': '1', '|': '1',
            'S': '5', 'G': '6', 'B': '8',
            'H': 'A', 'R': 'A'
        }
        
        for wrong, correct in corrections.items():
            text = text.replace(wrong, correct)
        
        # Valid card ranks
        valid_ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K']
        
        if text in valid_ranks:
            return text
        
        # Try partial matches
        for rank in valid_ranks:
            if rank in text or text in rank:
                return rank
        
        return None
    
    def detect_new_cards(self, image: np.ndarray) -> List[str]:
        """Detect new cards that appeared since last frame"""
        if image is None:
            return []

        card_regions = self.detect_card_regions(image)
        detected_ranks = []

        # Debug: Log number of regions found
        if len(card_regions) > 0:
            self.logger.debug(f"Found {len(card_regions)} potential card regions")

        for region in card_regions:
            rank = self.extract_card_rank(image, region)
            if rank:
                detected_ranks.append(rank)
                self.logger.debug(f"Detected card rank: {rank}")

        # Filter out cards we've already seen recently
        new_cards = []
        for rank in detected_ranks:
            if rank not in self.detected_cards_history[-10:]:  # Check last 10 detections
                new_cards.append(rank)
                self.logger.info(f"New card detected: {rank}")

        # Update history
        self.detected_cards_history.extend(new_cards)
        if len(self.detected_cards_history) > 50:  # Keep only recent history
            self.detected_cards_history = self.detected_cards_history[-50:]

        return new_cards
    
    def get_debug_image(self, image: np.ndarray) -> np.ndarray:
        """Get image with detected card regions highlighted for debugging"""
        if image is None:
            return None
            
        debug_img = image.copy()
        card_regions = self.detect_card_regions(image)
        
        for i, (x, y, w, h) in enumerate(card_regions):
            # Draw rectangle around detected card
            cv2.rectangle(debug_img, (x, y), (x+w, y+h), (0, 255, 0), 2)
            
            # Try to extract and display rank
            rank = self.extract_card_rank(image, (x, y, w, h))
            if rank:
                cv2.putText(debug_img, rank, (x, y-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        return debug_img
