# Blackjack Card Counting Bot

A real-time blackjack card counting application with screen recognition capabilities. This bot uses computer vision to detect cards from your screen and maintains accurate Hi-Lo count statistics.

## Features

- **Real-time Card Detection**: Uses OpenCV and OCR to detect cards from screen capture
- **Hi-Lo Counting System**: Implements the popular Hi-Lo card counting strategy
- **True Count Calculation**: Automatically calculates true count based on remaining decks
- **Bust Probability Calculator**: Shows probability of busting when hitting with current hand value
- **Comprehensive Statistics**: Displays running count, true count, remaining decks, and advantage estimation
- **Manual Card Entry**: Allows manual addition of cards for verification or backup
- **Debug Mode**: Visual debugging window to see card detection in real-time
- **Undo Functionality**: Remove incorrectly detected cards
- **Clean Gray UI**: Uniform gray interface that's easy on the eyes
- **Configurable Settings**: Customize screen region, detection parameters, and more

## Installation

1. **Clone or download the project files**

2. **Install Python dependencies**:

   ```bash
   pip install -r requirements.txt
   ```

3. **Install Tesseract OCR** (if using Tesseract instead of EasyOCR):
   - Download from: https://github.com/UB-Mannheim/tesseract/wiki
   - Install and note the installation path
   - Update the path in `card_detector.py` if needed

## Configuration

Edit `config.py` to customize:

- **Screen Region**: Set the area to capture for card detection
- **Deck Count**: Default is 8 decks (standard for most casinos)
- **Detection Parameters**: Adjust sensitivity and thresholds
- **GUI Settings**: Customize appearance and update intervals

## Usage

### GUI Mode (Recommended)

```bash
python main.py
```

### Console Mode (Testing)

```bash
python main.py --no-gui
```

### Debug Mode

```bash
python main.py --debug
```

## How It Works

1. **Screen Capture**: Continuously captures the specified screen region
2. **Card Detection**: Uses computer vision to identify card-like shapes
3. **OCR Recognition**: Extracts card ranks using optical character recognition
4. **Count Tracking**: Updates Hi-Lo count and calculates statistics
5. **Display Updates**: Shows real-time statistics in the GUI

## Card Counting System

The bot uses the **Hi-Lo counting system**:

- **Low cards (2-6)**: +1
- **Neutral cards (7-9)**: 0
- **High cards (10-A)**: -1

### Key Metrics

- **Running Count**: Sum of all card values seen
- **True Count**: Running count ÷ Remaining decks
- **Deck Penetration**: Percentage of cards dealt
- **Advantage Estimate**: Based on true count value

## GUI Controls

- **Start/Stop Detection**: Toggle automatic card detection
- **Reset Count**: Clear all counts and start fresh
- **Undo Last**: Remove the most recently added card
- **Manual Entry**: Add cards manually by typing rank

## Tips for Best Results

1. **Screen Setup**: Ensure cards are clearly visible and well-lit
2. **Region Configuration**: Adjust screen capture region to focus on card area
3. **Stable View**: Minimize camera movement and ensure consistent card positioning
4. **Manual Verification**: Use manual entry to correct any detection errors
5. **Regular Reset**: Reset count when a new shoe begins

## Troubleshooting

### Common Issues

1. **Cards Not Detected**:

   - Check screen region settings in `config.py`
   - Ensure cards are clearly visible
   - Adjust detection thresholds

2. **Incorrect Card Recognition**:

   - Try switching between EasyOCR and Tesseract in config
   - Improve lighting and card visibility
   - Use manual entry for corrections

3. **Performance Issues**:
   - Reduce screen capture region size
   - Increase detection interval
   - Close unnecessary applications

### Debug Mode

Run with `--debug` flag to see detailed logging and detection information.

## Legal Disclaimer

This software is for educational purposes only. Card counting is legal but may be prohibited by casino policies. Use responsibly and in accordance with local laws and regulations.

## Requirements

- Python 3.7+
- Windows/Linux/macOS
- Webcam or screen access
- Good lighting conditions for card visibility

## License

This project is for educational use only. Please use responsibly.
